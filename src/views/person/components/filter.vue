<template>
  <el-form ref="form" label-width="100px" :model="formData">
    <div class="flex">
      <el-form-item class="span-4" label="人才检索" prop="userKeyword">
        <el-input
          @keyup.enter="handleSearch"
          v-model="formData.userKeyword"
          placeholder="输入人才姓名/ID/用户名"
        ></el-input>
      </el-form-item>
      <el-form-item class="span-4" label="手机号" prop="mobile">
        <el-input @keyup.enter="handleSearch" v-model="formData.mobile" placeholder="请输入手机号">
          <template #prepend>
            <el-select
              v-model="formData.mobileCode"
              style="width: 83px"
              clearable
              placeholder="号段"
            >
              <el-option-group v-for="{ type, list } in prefixOptions" :key="type" :label="type">
                <el-option v-for="{ country, code } in list" :key="code" :value="code">
                  <span style="float: left">{{ country }}</span>
                  <span style="float: right"> {{ code }} </span>
                </el-option>
              </el-option-group>
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item class="span-4" label="最高学历" prop="educationId">
        <Education v-model="formData.educationId" placeholder="不限" />
      </el-form-item>
      <el-form-item class="span-4" label-width="10px">
        <div class="nowrap">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button plain @click="handleResetField">重置</el-button>
          <el-link :underline="false" class="ml-10" @click="showMore = !showMore">展开</el-link>
        </div>
      </el-form-item>
    </div>

    <div v-show="showMore">
      <div class="flex">
        <el-form-item class="span-4" label="身份" prop="identityType">
          <IdentityType v-model="formData.identityType" />
        </el-form-item>
        <el-form-item
          v-if="formData.identityType !== '2'"
          class="span-4"
          label="工作年限"
          prop="workYears"
        >
          <WorkExperience
            v-model="formData.workYears"
            :disabled="formData.identityType === '-1'"
            placeholder="不限"
          />
        </el-form-item>
        <el-form-item
          v-if="formData.identityType === '2'"
          class="span-4"
          label="毕业时间"
          prop="graduateDate"
        >
          <DatePickerRange
            v-model:start="formData.graduateBeginDate"
            v-model:end="formData.graduateEndDate"
          />
        </el-form-item>
        <el-form-item class="span-4" label="求职状态" prop="workStatus">
          <JobStatus v-model="formData.workStatus" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="户籍/国籍" prop="householdRegisterId">
          <NativePlace v-model="formData.householdRegisterId" placeholder="不限" />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="现居住地" prop="areaId">
          <Region multiple collapse-tags v-model="formData.areaId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="注册时间">
          <DatePickerRange
            v-model:start="formData.startCreateTime"
            v-model:end="formData.endCreateTime"
          />
        </el-form-item>
        <el-form-item class="span-4" label="最近登录时间">
          <DatePickerRange
            v-model:start="formData.startLastLoginTime"
            v-model:end="formData.endLastLoginTime"
          />
        </el-form-item>
        <el-form-item class="span-4" label="最近更新时间">
          <DatePickerRange
            v-model:start="formData.startLastUpdateTime"
            v-model:end="formData.endLastUpdateTime"
          />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="求职意向" prop="jobCategory">
          <JobCategory :multiple="false" v-model="formData.jobCategory" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="意向城市" prop="cityId">
          <Region multiple collapse-tags v-model="formData.cityId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="工作性质" prop="natureType">
          <WorkNature v-model="formData.natureType" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="薪资要求" prop="wageId">
          <Wage v-model="formData.wageId" placeholder="不限" />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="到岗时间" prop="arriveDateType">
          <ArriveDate v-model="formData.arriveDateType" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="是否开通代投" prop="isProxyDeliver">
          <HelpSend v-model="formData.isProxyDeliver" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="海外经历" prop="isAbroad">
          <AbroadExperience v-model="formData.isAbroad" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="985/211" prop="isProjectSchool">
          <GoodHigeSchool v-model="formData.isProjectSchool" placeholder="不限" />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="博士后经历" prop="isPostdoc">
          <Postdoc v-model="formData.isPostdoc" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="组合" prop="textCombination">
          <el-input
            @keyup.enter="handleSearch"
            v-model="formData.textCombination"
            placeholder="研究方向/学术成果/荣誉奖励"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-4" label="研究方向" prop="researchDirection">
          <el-input
            @keyup.enter="handleSearch"
            v-model="formData.researchDirection"
            placeholder="研究方向"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-4" label="学术成果" prop="academicAchievement">
          <el-input
            @keyup.enter="handleSearch"
            v-model="formData.academicAchievement"
            placeholder="学术成果"
          ></el-input>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="荣誉奖励" prop="reward">
          <el-input
            @keyup.enter="handleSearch"
            v-model="formData.reward"
            placeholder="荣誉奖励"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-4" label="年龄" prop="ageId">
          <Age el-type="select" v-model="formData.ageId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="职称" prop="titleId">
          <LevelTitle el-type="cascader" multiple v-model="formData.titleId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="性别" prop="gender">
          <Gender v-model="formData.gender" placeholder="不限" />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="注册来源" prop="sourceType">
          <MemberSourceType v-model="formData.sourceType" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="毕业院校" prop="school">
          <el-input
            @keyup.enter="handleSearch"
            v-model="formData.school"
            placeholder="毕业院校"
          ></el-input>
        </el-form-item>
        <el-form-item class="span-4" label="是否开启订阅" prop="isSubscribe">
          <el-select v-model="formData.isSubscribe" class="w100" filterable clearable>
            <el-option label="是" value="1">是</el-option>
            <el-option label="否" value="2">否</el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="span-4" label="简历刷新时间">
          <DatePickerRange
            v-model:start="formData.refreshTimeStart"
            v-model:end="formData.refreshTimeEnd"
          />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="学科专业" prop="majorId">
          <MajorCategory v-model="formData.majorId" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="最小完整度" prop="startComplete">
          <el-input-number v-model="formData.startComplete" :step="10" />
        </el-form-item>
        <el-form-item class="span-4" label="最大完整度" prop="endComplete">
          <el-input-number v-model="formData.endComplete" :step="10" />
        </el-form-item>
        <el-form-item class="span-4" label="是否绑定微信" prop="isWxBind">
          <WxBind placeholder="不限" v-model="formData.isWxBind" />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="最近活跃时间">
          <DatePickerRange
            v-model:start="formData.startLastActiveTime"
            v-model:end="formData.endLastActiveTime"
          />
        </el-form-item>
        <el-form-item class="span-4" label="简历开放状态" prop="isResumeStatus">
          <resumeStatus v-model="formData.isResumeStatus" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="会员类型" prop="vipType">
          <vipType v-model="formData.vipType" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="人才标签" prop="resumeTagId">
          <el-select
            v-model="formData.resumeTagId"
            class="w100"
            placeholder="不限"
            multiple
            clearable
          >
            <el-option
              v-for="item in taglist"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              >{{ item.label }}</el-option
            >
          </el-select>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item class="span-4" label="简历类型" prop="resumeType">
          <resumeType v-model="formData.resumeType" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="项目经历类型" prop="resumeType">
          <projectCate v-model="formData.projectCate" placeholder="不限" />
        </el-form-item>
        <el-form-item class="span-4" label="账号状态" prop="memberStatus">
          <el-select
            v-model="formData.memberStatus"
            class="w100"
            filterable
            clearable
            placeholder="不限"
          >
            <el-option
              v-for="item in memberStatusOptions"
              :key="item.k"
              :label="item.v"
              :value="item.k"
            />
          </el-select>
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script lang="ts">
import { reactive, toRefs, ref, defineComponent, nextTick, watch, onMounted } from 'vue'
import MajorCategory from '/@select/majorCategory.vue'
import Education from '/@select/educationSearch.vue'
import Region from '/@select/region.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import AbroadExperience from '/@select/abroadExperience.vue'
import WorkExperience from '/@select/workExperience.vue'
import Age from '/@select/age.vue'
import Gender from '/@select/gender.vue'
import JobStatus from '/@select/personStatus.vue'
import NativePlace from '/@select/nativePlace.vue'
import JobCategory from '/@select/jobCategory.vue'
import WorkNature from '/@select/workNature.vue'
import Wage from '/@select/wage.vue'
import HelpSend from '/@select/helpSend.vue'
import GoodHigeSchool from '/@select/projectSchool.vue'
import Postdoc from '/@select/postdoc.vue'
import LevelTitle from '/@select/levelTitle.vue'
import MemberSourceType from '/@select/memberSourceType.vue'
import ArriveDate from '/@select/arriveDate.vue'
import WxBind from '/@/components/base/select/wxBind.vue'
import resumeStatus from '/@select/resumeStatus.vue'
import vipType from '/@select/vipType.vue'
import IdentityType from '/@select/identityType.vue'
import resumeType from '/@select/resumeTypeSearch.vue'
import projectCate from '/@select/projectCate.vue'

import { getCountrytMobileCode, getMemberStatusList } from '/@/api/config'
import { getResumeTagList } from '/@/api/person'

export default defineComponent({
  name: 'jobQuery',
  components: {
    MajorCategory,
    Education,
    Region,
    DatePickerRange,
    AbroadExperience,
    WorkExperience,
    Age,
    Gender,
    JobStatus,
    NativePlace,
    JobCategory,
    WorkNature,
    Wage,
    HelpSend,
    GoodHigeSchool,
    LevelTitle,
    Postdoc,
    MemberSourceType,
    ArriveDate,
    resumeStatus,
    vipType,
    WxBind,
    IdentityType,
    resumeType,
    projectCate
  },
  emits: ['search', 'download'],
  setup(props, { emit }) {
    const state = reactive({
      prefixOptions: <any>[],
      region: '',
      loading: false,
      showMore: false,
      formData: {
        // 人才标签
        resumeTagId: [],
        // 用户名、用户id或者姓名
        userKeyword: '',
        vipType: '',
        mobileCode: '',
        mobile: '',
        // 毕业院校
        school: '',
        // 最高学历
        educationId: '',
        // 学科专业
        majorId: '',
        // 所在地区
        areaId: [],
        // 毕业院校
        shcoolName: '',
        // 身份
        identityType: '',
        // 毕业时间
        graduateBeginDate: '',
        graduateEndDate: '',
        // 工作年限
        workYears: '',
        // 求职状态
        workStatus: '',
        // 户籍国籍
        householdRegisterId: '',
        // 创建时间
        startCreateTime: '',
        endCreateTime: '',
        // 最近登录时间
        startLastLoginTime: '',
        endLastLoginTime: '',
        // 最近更新时间
        startLastUpdateTime: '',
        endLastUpdateTime: '',
        // 最近活跃时间
        startLastActiveTime: '',
        endLastActiveTime: '',
        // 意向职能
        jobCategory: '',
        // 意向城市id
        cityId: [],
        // 工作性质
        natureType: '',
        // 薪资要求
        wageId: '',
        // 到岗时间
        arriveDateType: '',
        // 是否代投
        isResumeStatus: '',
        // 是否代投
        isProxyDeliver: '',
        // 海外经历
        isAbroad: '',
        // 职位订阅
        isSubscribe: '',
        // 简历刷新时间
        refreshTimeStart: '',
        refreshTimeEnd: '',
        // 211
        isProjectSchool: '',
        // 是否有博士后经历
        isPostdoc: '',
        // 年龄
        ageId: '',
        // 职称
        titleId: [],
        // 性别
        gender: '',
        // 1倒序）
        sortCreateTime: '',
        // 1倒序）
        sortLastLoginTime: '',
        // 排序
        sortComplete: '',
        // 注册来源
        sourceType: '',
        // 简历完整度
        startComplete: 0,
        // 简历完整度
        endComplete: 100,
        // 是否绑定微信
        isWxBind: '',
        // 组合文案
        textCombination: '',
        // 研究方向
        researchDirection: '',
        // 学术成果
        academicAchievement: '',
        // 荣誉奖励
        reward: '',
        // 简历类型
        resumeType: '',
        // 项目经历类型
        projectCate: '',
        // 账号状态
        memberStatus: '',
        // 条数
        pageSize: '',
        // 页码
        page: ''
      },
      resetFormData: {},
      // 标签列表
      taglist: [],
      // 账号状态选项
      memberStatusOptions: []
    })

    onMounted(() => {
      getResumeTagListFun({})
      getMemberStatusListFun()
    })

    watch(
      () => state.formData.identityType,
      (val: any) => {
        if (val === '1') {
          state.formData.graduateBeginDate = ''
          state.formData.graduateEndDate = ''
        } else if (val === '2') {
          state.formData.workYears = ''
        } else if (val === '0') {
          state.formData.graduateBeginDate = ''
          state.formData.graduateEndDate = ''
        } else {
          state.formData.graduateBeginDate = ''
          state.formData.graduateEndDate = ''
          state.formData.workYears = ''
        }
      }
    )

    const getResumeTagListFun = (obj) => {
      getResumeTagList(obj).then((res) => {
        // console.log("res",res);
        state.taglist = res || []
      })
    }

    const getMemberStatusListFun = () => {
      getMemberStatusList()
        .then((res) => {
          // 由于响应拦截器已经返回了data部分，所以res就是数据数组
          state.memberStatusOptions = res || []
        })
        .catch((error) => {
          console.error('获取账号状态列表失败:', error)
        })
    }

    const form = ref()

    const getMobileCode = async () => {
      state.prefixOptions = await getCountrytMobileCode()
    }
    getMobileCode()

    const arrayToStringData = (data: any) => {
      return Object.keys(data).reduce((previous: any, current: string) => {
        const tmp = { ...previous }
        const value = data[current]

        tmp[current] = Array.isArray(value) ? value.join() : value
        return tmp
      }, {})
    }

    const formatHouseholdRegisterId = (data) => {
      return Object.values(data).reduce((previous: any[], current: any) => {
        const tmp = [...previous]
        tmp.push(Array.isArray(current) ? current[current.length - 1] : current)
        return [...new Set(tmp)]
      }, [])
    }

    const emitSearch = () => {
      const {
        formData,
        formData: { householdRegisterId, identityType }
      } = state
      const params: any = { ...formData, identityType: identityType === '-1' ? '' : identityType }

      if (householdRegisterId) {
        params.householdRegisterId = formatHouseholdRegisterId(householdRegisterId)
      }

      emit('search', arrayToStringData(params))
    }

    // 重置
    const handleResetField = () => {
      form.value.resetFields()
      state.formData.mobileCode = ''
      state.formData.startCreateTime = ''
      state.formData.endCreateTime = ''
      state.formData.startLastLoginTime = ''
      state.formData.endLastLoginTime = ''
      state.formData.startLastUpdateTime = ''
      state.formData.endLastUpdateTime = ''
      state.formData.startLastActiveTime = ''
      state.formData.endLastActiveTime = ''
      state.formData.refreshTimeStart = ''
      state.formData.refreshTimeEnd = ''
      state.formData.resumeType = ''
      state.formData.projectCate = ''
      state.formData.memberStatus = ''

      nextTick(() => {
        emitSearch()
      })
    }

    const handleSearch = () => {
      emitSearch()
    }

    return {
      handleSearch,
      handleResetField,
      getResumeTagListFun,
      form,
      ...toRefs(state)
    }
  }
})
</script>

<style scoped lang="scss">
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.el-button--small {
  padding: 9px 10px;
}
</style>
