<template>
  <div class="job-file-upload-container">
    <!-- 隐藏的文件选择器 -->
    <el-upload
      ref="uploadRef"
      class="hidden-upload"
      multiple
      :auto-upload="false"
      :limit="10"
      :show-file-list="false"
      :on-change="handleFileChange"
      :on-exceed="fileExceed"
      :before-upload="beforeFileUpload"
      style="display: none"
    >
    </el-upload>

    <!-- 上传进度遮罩层 -->
    <div class="upload-overlay" v-if="isUploading">
      <div class="upload-progress-content">
        <el-icon class="upload-icon rotating">
          <Loading />
        </el-icon>
        <div class="progress-text">正在上传附件...</div>
        <el-progress
          :percentage="overallProgress"
          :status="overallProgress === 100 ? 'success' : ''"
          class="progress-bar"
        >
          <template #default="{ percentage }">
            <span class="progress-detail">
              {{ uploadedCount }}/{{ totalFiles }} 文件已完成 ({{ percentage }}%)
            </span>
          </template>
        </el-progress>

        <!-- 当前上传文件信息 -->
        <div class="current-file" v-if="currentUploadingFile">
          <el-icon class="file-icon">
            <Document />
          </el-icon>
          <span class="file-name">{{ currentUploadingFile.name }}</span>
          <span class="file-size">({{ formatFileSize(currentUploadingFile.size) }})</span>
        </div>
      </div>
    </div>

    <!-- 已上传文件列表 -->
    <div class="file-list" ref="fileListRef" :class="{ uploading: isUploading }">
      <div class="file" v-for="(item, index) in fileList" :key="index" :data-id="item.id">
        <div class="drag-handle">≡</div>
        <div class="file-name">{{ item.name }}</div>
        <span class="remove-btn" @click="removeFile(item.id)">×</span>
      </div>

      <!-- 空状态提示 -->
      <div class="empty-state" v-if="fileList.length === 0 && !isUploading">
        <el-icon class="empty-icon">
          <Document />
        </el-icon>
        <span class="empty-text">暂无附件</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ElMessage } from 'element-plus'
import { computed, defineComponent, reactive, ref, toRefs, onMounted, nextTick } from 'vue'
import Sortable from 'sortablejs'
import { Document, Clock, Loading, CircleCheck, CircleClose } from '@element-plus/icons-vue'

// 文件状态类型定义
type FileStatus = 'waiting' | 'uploading' | 'success' | 'error'

interface PendingFile {
  uid: string
  name: string
  size: number
  raw: File
  status: FileStatus
  id?: string
  error?: string
}

export default defineComponent({
  name: 'jobFileUpload',

  components: {
    Document,
    Clock,
    Loading,
    CircleCheck,
    CircleClose
  },

  props: {
    modelValue: {
      type: Array,
      default: () => []
    }
  },

  setup(props, { emit }) {
    const state = reactive({
      isUploading: false,
      pendingFiles: [] as PendingFile[],
      currentUploadingFile: null as PendingFile | null,
      fileList: <any>computed({
        get() {
          return props.modelValue.length ? props.modelValue : []
        },
        set(val) {
          emit('update:modelValue', val)
        }
      })
    })

    const fileListRef = ref(null)
    const uploadRef = ref()

    // 计算上传进度
    const overallProgress = computed(() => {
      if (state.pendingFiles.length === 0) return 0
      const completedFiles = state.pendingFiles.filter(
        (f) => f.status === 'success' || f.status === 'error'
      ).length
      return Math.round((completedFiles / state.pendingFiles.length) * 100)
    })

    const uploadedCount = computed(() => {
      return state.pendingFiles.filter((f) => f.status === 'success').length
    })

    const totalFiles = computed(() => {
      return state.pendingFiles.length
    })

    // 直接打开文件选择器
    const openJobFileUpload = () => {
      // 触发隐藏的文件选择器
      const input = uploadRef.value?.$el?.querySelector('input[type="file"]')
      if (input) {
        input.click()
      }
    }

    // 文件大小格式化
    const formatFileSize = (bytes: number): string => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 检查文件是否重复
    const isDuplicateFile = (file: File): boolean => {
      return (
        state.pendingFiles.some((f) => f.name === file.name && f.size === file.size) ||
        state.fileList.some((f: any) => f.name === file.name)
      )
    }

    // 验证文件类型
    const validateFileType = (file: File): boolean => {
      const allowedTypes = [
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/pdf',
        'application/x-rar-compressed',
        'application/zip',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'text/plain'
      ]

      const fileExtension = file.name.split('.').pop()?.toLowerCase()
      const allowedExtensions = [
        'doc',
        'docx',
        'xls',
        'xlsx',
        'pdf',
        'rar',
        'zip',
        'ppt',
        'pptx',
        'jpg',
        'jpeg',
        'png',
        'txt'
      ]

      return (
        allowedTypes.includes(file.type) ||
        (fileExtension && allowedExtensions.includes(fileExtension))
      )
    }

    // 文件选择处理 - 选择后立即开始上传
    const handleFileSelect = (file: any, fileList: any[]) => {
      const rawFile = file.raw

      // 验证文件大小
      if (!beforeFileUpload(rawFile)) {
        // 清空文件选择器
        nextTick(() => {
          uploadRef.value?.clearFiles()
        })
        return
      }

      // 验证文件类型
      if (!validateFileType(rawFile)) {
        ElMessage.error('不支持的文件类型')
        // 清空文件选择器
        nextTick(() => {
          uploadRef.value?.clearFiles()
        })
        return
      }

      // 检查重复文件
      if (isDuplicateFile(rawFile)) {
        ElMessage.warning('文件已存在，请勿重复添加')
        // 清空文件选择器
        nextTick(() => {
          uploadRef.value?.clearFiles()
        })
        return
      }

      // 添加到待上传列表
      const pendingFile: PendingFile = {
        uid: file.uid,
        name: rawFile.name,
        size: rawFile.size,
        raw: rawFile,
        status: 'waiting'
      }

      state.pendingFiles.push(pendingFile)

      // 清空上传组件的文件列表
      nextTick(() => {
        uploadRef.value?.clearFiles()
      })
    }

    // 处理多个文件选择完成后，立即开始上传
    const handleFileListChange = () => {
      // 如果有待上传文件且当前没有在上传，则立即开始上传
      if (state.pendingFiles.length > 0 && !state.isUploading) {
        nextTick(() => {
          startAutoUpload()
        })
      }
    }

    // 单个文件上传
    const uploadSingleFile = async (pendingFile: PendingFile): Promise<boolean> => {
      const formData = new FormData()
      formData.append('file', pendingFile.raw)

      try {
        pendingFile.status = 'uploading'

        const response = await fetch('/upload/job-appendix', {
          method: 'POST',
          body: formData
        })
        const res = await response.json()

        if (res.result === 1) {
          pendingFile.status = 'success'
          pendingFile.id = res.data.id
          return true
        } else {
          pendingFile.status = 'error'
          pendingFile.error = res.msg || '上传失败'
          ElMessage.error(`文件 ${pendingFile.name} 上传失败: ${res.msg || '未知错误'}`)
          return false
        }
      } catch (error) {
        pendingFile.status = 'error'
        pendingFile.error = '网络错误，上传失败'
        ElMessage.error(`文件 ${pendingFile.name} 上传失败: 网络错误`)
        return false
      }
    }

    // 自动上传 - 选择文件后立即开始
    const startAutoUpload = async () => {
      if (state.pendingFiles.length === 0) return

      state.isUploading = true
      let successCount = 0
      let errorCount = 0

      // 逐个上传文件
      for (const file of state.pendingFiles) {
        if (file.status === 'waiting') {
          state.currentUploadingFile = file
          const success = await uploadSingleFile(file)
          if (success) {
            successCount++
            // 成功上传的文件立即添加到文件列表
            const newFile = { name: file.name, id: file.id }
            state.fileList = [...state.fileList, newFile]
          } else {
            errorCount++
          }
        }
      }

      state.isUploading = false
      state.currentUploadingFile = null

      // 清空待上传列表
      state.pendingFiles = []

      // 显示上传结果
      if (errorCount === 0) {
        ElMessage.success(`成功上传 ${successCount} 个附件`)
      } else if (successCount > 0) {
        ElMessage.warning(`${successCount}个文件上传成功，${errorCount}个文件上传失败`)
      } else {
        ElMessage.error('文件上传失败，请重试')
      }
    }

    // 文件数量超限处理
    const fileExceed = () => {
      ElMessage.warning('职位附件不能超过10个')
    }

    // 文件上传前验证
    const beforeFileUpload = (rawFile: any) => {
      if (rawFile.size / 1024 / 1024 > 20) {
        ElMessage.error('文件过大，单个文件不能超过20M')
        return false
      }
      return true
    }

    // 移除已上传文件
    const removeFile = async (id: string) => {
      state.fileList = state.fileList.filter((item: any) => item.id !== id)
    }

    // 清空所有文件
    const clear = () => {
      state.fileList = []
    }

    // 监听文件选择变化，触发自动上传
    const handleFileChange = (file: any, fileList: any[]) => {
      // 处理单个文件选择
      handleFileSelect(file, fileList)

      // 延迟一点时间，确保所有文件都已添加到待上传列表
      setTimeout(() => {
        if (state.pendingFiles.length > 0 && !state.isUploading) {
          startAutoUpload()
        }
      }, 100)
    }

    onMounted(() => {
      nextTick(() => {
        if (fileListRef.value) {
          new Sortable(fileListRef.value, {
            animation: 150,
            handle: '.drag-handle',
            onEnd({ oldIndex, newIndex }) {
              const itemsCopy = [...state.fileList]
              const [removed] = itemsCopy.splice(oldIndex, 1)
              itemsCopy.splice(newIndex, 0, removed)
              // 强制更新数组引用以触发响应式更新
              state.fileList = []
              nextTick(() => {
                state.fileList = itemsCopy
              })
            }
          })
        }
      })
    })

    return {
      ...toRefs(state),
      fileListRef,
      uploadRef,
      overallProgress,
      uploadedCount,
      totalFiles,
      openJobFileUpload,
      handleFileChange,
      removeFile,
      clear,
      fileExceed,
      beforeFileUpload,
      formatFileSize
    }
  }
})
</script>

<style lang="scss" scoped>
.job-file-upload-container {
  position: relative;
  min-height: 60px;
}

.hidden-upload {
  display: none !important;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
  border: 2px dashed #409eff;

  .upload-progress-content {
    text-align: center;
    padding: 20px;

    .upload-icon {
      font-size: 32px;
      color: #409eff;
      margin-bottom: 12px;

      &.rotating {
        animation: rotating 2s linear infinite;
      }
    }

    .progress-text {
      font-size: 16px;
      color: #303133;
      margin-bottom: 16px;
      font-weight: 500;
    }

    .progress-bar {
      margin-bottom: 12px;
      width: 300px;

      .progress-detail {
        font-size: 12px;
        color: #606266;
      }
    }

    .current-file {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #606266;

      .file-icon {
        margin-right: 8px;
        color: #909399;
      }

      .file-name {
        margin-right: 4px;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-size {
        color: #909399;
      }
    }
  }
}

.file-list {
  min-height: 60px;
  transition: opacity 0.3s ease;

  &.uploading {
    opacity: 0.3;
  }

  .file {
    display: flex;
    align-items: center;
    margin: 10px 0;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;

    &:hover {
      background-color: #ecf5ff;
      border-color: #b3d8ff;
    }

    .drag-handle {
      cursor: move;
      margin-right: 12px;
      color: #909399;
      font-size: 14px;

      &:hover {
        color: #409eff;
      }
    }

    .file-name {
      flex: 1;
      color: #303133;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .remove-btn {
      cursor: pointer;
      color: #f56c6c;
      font-size: 18px;
      font-weight: bold;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f56c6c;
        color: white;
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #909399;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 12px;
      opacity: 0.5;
    }

    .empty-text {
      font-size: 14px;
    }
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
