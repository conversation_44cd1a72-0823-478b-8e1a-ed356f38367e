<template>
  <div>
    <el-dialog v-model="jobFileUploadVisbled" title="附件上传" width="600px">
      <!-- 文件选择区域 -->
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        multiple
        :auto-upload="false"
        :limit="10"
        :show-file-list="false"
        :on-change="handleFileSelect"
        :on-exceed="fileExceed"
        :before-upload="beforeFileUpload"
      >
        <div>请上传doc、docx、xls、xlsx、pdf、rar、zip、ppt、jpg、jpeg、png、txt类型的文件</div>
        <div class="el-upload__text">点击或将文件拖拽到这里选择文件</div>
        <div class="el-upload__text">单个文件最大20M，最多可选择10个文件</div>
      </el-upload>

      <!-- 待上传文件列表 -->
      <div class="pending-files" v-if="pendingFiles.length > 0">
        <div class="list-header">
          <span>待上传文件 ({{ pendingFiles.length }})</span>
          <div class="header-actions">
            <el-button size="small" @click="clearPendingFiles">清空</el-button>
            <el-button
              type="primary"
              size="small"
              :loading="isUploading"
              :disabled="pendingFiles.length === 0"
              @click="startBatchUpload"
            >
              {{ isUploading ? '上传中...' : '开始上传' }}
            </el-button>
          </div>
        </div>

        <!-- 整体上传进度 -->
        <div class="overall-progress" v-if="isUploading">
          <el-progress
            :percentage="overallProgress"
            :status="overallProgress === 100 ? 'success' : ''"
          >
            <template #default="{ percentage }">
              <span class="progress-text">
                {{ uploadedCount }}/{{ totalFiles }} 文件已完成 ({{ percentage }}%)
              </span>
            </template>
          </el-progress>
        </div>

        <!-- 文件列表 -->
        <div class="file-item" v-for="(file, index) in pendingFiles" :key="file.uid">
          <div class="file-info">
            <el-icon class="file-icon">
              <Document />
            </el-icon>
            <div class="file-details">
              <div class="file-name" :title="file.name">{{ file.name }}</div>
              <div class="file-size">{{ formatFileSize(file.size) }}</div>
            </div>
          </div>

          <div class="file-status">
            <!-- 等待状态 -->
            <div v-if="file.status === 'waiting'" class="status-waiting">
              <el-icon><Clock /></el-icon>
              <span>等待中</span>
            </div>

            <!-- 上传中状态 -->
            <div v-else-if="file.status === 'uploading'" class="status-uploading">
              <el-icon class="rotating"><Loading /></el-icon>
              <span>上传中</span>
            </div>

            <!-- 成功状态 -->
            <div v-else-if="file.status === 'success'" class="status-success">
              <el-icon><CircleCheck /></el-icon>
              <span>已完成</span>
            </div>

            <!-- 失败状态 -->
            <div v-else-if="file.status === 'error'" class="status-error">
              <el-icon><CircleClose /></el-icon>
              <span>失败</span>
              <el-button size="small" text type="primary" @click="retryUpload(file)"
                >重试</el-button
              >
            </div>
          </div>

          <div class="file-actions">
            <el-button
              size="small"
              text
              type="danger"
              @click="removePendingFile(index)"
              :disabled="file.status === 'uploading'"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="jobFileUploadVisbled = false">取消</el-button>
          <el-button type="primary" @click="confirmUpload" :disabled="uploadedFiles.length === 0">
            确认 ({{ uploadedFiles.length }}个文件)
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 已上传文件列表 -->
    <div class="file-list" ref="fileListRef">
      <div class="file" v-for="(item, index) in fileList" :key="index" :data-id="item.id">
        <div class="drag-handle">≡</div>
        <div>{{ item.name }}</div>
        <span @click="removeFile(item.id)">X</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { ElMessage } from 'element-plus'
import { computed, defineComponent, reactive, ref, toRefs, onMounted, nextTick } from 'vue'
import Sortable from 'sortablejs'
import { Document, Clock, Loading, CircleCheck, CircleClose } from '@element-plus/icons-vue'

// 文件状态类型定义
type FileStatus = 'waiting' | 'uploading' | 'success' | 'error'

interface PendingFile {
  uid: string
  name: string
  size: number
  raw: File
  status: FileStatus
  id?: string
  error?: string
}

export default defineComponent({
  name: 'jobFileUpload',

  components: {
    Document,
    Clock,
    Loading,
    CircleCheck,
    CircleClose
  },

  props: {
    modelValue: {
      type: Array,
      default: () => []
    }
  },

  setup(props, { emit }) {
    const state = reactive({
      jobFileUploadVisbled: false,
      isUploading: false,
      pendingFiles: [] as PendingFile[],
      uploadedFiles: [] as any[],
      fileList: <any>computed({
        get() {
          return props.modelValue.length ? props.modelValue : []
        },
        set(val) {
          emit('update:modelValue', val)
        }
      })
    })

    const fileListRef = ref(null)
    const uploadRef = ref()

    // 计算上传进度
    const overallProgress = computed(() => {
      if (state.pendingFiles.length === 0) return 0
      const completedFiles = state.pendingFiles.filter(
        (f) => f.status === 'success' || f.status === 'error'
      ).length
      return Math.round((completedFiles / state.pendingFiles.length) * 100)
    })

    const uploadedCount = computed(() => {
      return state.pendingFiles.filter((f) => f.status === 'success').length
    })

    const totalFiles = computed(() => {
      return state.pendingFiles.length
    })

    const openJobFileUpload = () => {
      state.jobFileUploadVisbled = true
      // 清空之前的待上传文件
      state.pendingFiles = []
      state.uploadedFiles = []
    }

    // 文件大小格式化
    const formatFileSize = (bytes: number): string => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 检查文件是否重复
    const isDuplicateFile = (file: File): boolean => {
      return (
        state.pendingFiles.some((f) => f.name === file.name && f.size === file.size) ||
        state.fileList.some((f: any) => f.name === file.name)
      )
    }

    // 验证文件类型
    const validateFileType = (file: File): boolean => {
      const allowedTypes = [
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/pdf',
        'application/x-rar-compressed',
        'application/zip',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'text/plain'
      ]

      const fileExtension = file.name.split('.').pop()?.toLowerCase()
      const allowedExtensions = [
        'doc',
        'docx',
        'xls',
        'xlsx',
        'pdf',
        'rar',
        'zip',
        'ppt',
        'pptx',
        'jpg',
        'jpeg',
        'png',
        'txt'
      ]

      return (
        allowedTypes.includes(file.type) ||
        (fileExtension && allowedExtensions.includes(fileExtension))
      )
    }

    // 文件选择处理
    const handleFileSelect = (file: any, fileList: any[]) => {
      const rawFile = file.raw

      // 验证文件大小
      if (!beforeFileUpload(rawFile)) {
        return
      }

      // 验证文件类型
      if (!validateFileType(rawFile)) {
        ElMessage.error('不支持的文件类型')
        return
      }

      // 检查重复文件
      if (isDuplicateFile(rawFile)) {
        ElMessage.warning('文件已存在，请勿重复添加')
        return
      }

      // 添加到待上传列表
      const pendingFile: PendingFile = {
        uid: file.uid,
        name: rawFile.name,
        size: rawFile.size,
        raw: rawFile,
        status: 'waiting'
      }

      state.pendingFiles.push(pendingFile)

      // 清空上传组件的文件列表，避免显示
      nextTick(() => {
        uploadRef.value?.clearFiles()
      })
    }

    // 单个文件上传
    const uploadSingleFile = async (pendingFile: PendingFile): Promise<boolean> => {
      const formData = new FormData()
      formData.append('file', pendingFile.raw)

      try {
        pendingFile.status = 'uploading'

        const response = await fetch('/upload/job-appendix', {
          method: 'POST',
          body: formData
        })
        const res = await response.json()

        if (res.result === 1) {
          pendingFile.status = 'success'
          pendingFile.id = res.data.id

          // 添加到已上传文件列表
          const newFile = { name: res.data.name, id: res.data.id }
          state.uploadedFiles.push(newFile)

          return true
        } else {
          pendingFile.status = 'error'
          pendingFile.error = res.msg || '上传失败'
          return false
        }
      } catch (error) {
        pendingFile.status = 'error'
        pendingFile.error = '网络错误，上传失败'
        return false
      }
    }

    // 批量上传
    const startBatchUpload = async () => {
      if (state.pendingFiles.length === 0) return

      state.isUploading = true
      state.uploadedFiles = []

      // 重置所有文件状态为等待
      state.pendingFiles.forEach((file) => {
        if (file.status === 'error') {
          file.status = 'waiting'
          file.error = undefined
        }
      })

      // 逐个上传文件
      for (const file of state.pendingFiles) {
        if (file.status === 'waiting') {
          await uploadSingleFile(file)
        }
      }

      state.isUploading = false

      // 检查上传结果
      const successCount = state.pendingFiles.filter((f) => f.status === 'success').length
      const errorCount = state.pendingFiles.filter((f) => f.status === 'error').length

      if (errorCount === 0) {
        ElMessage.success(`所有文件上传成功 (${successCount}个)`)
      } else if (successCount > 0) {
        ElMessage.warning(`${successCount}个文件上传成功，${errorCount}个文件上传失败`)
      } else {
        ElMessage.error('所有文件上传失败')
      }
    }

    // 重试上传
    const retryUpload = async (file: PendingFile) => {
      await uploadSingleFile(file)
    }

    // 确认上传完成
    const confirmUpload = () => {
      // 将成功上传的文件添加到文件列表
      state.fileList = [...state.fileList, ...state.uploadedFiles]

      // 关闭弹窗
      state.jobFileUploadVisbled = false

      // 清空待上传列表
      state.pendingFiles = []
      state.uploadedFiles = []

      ElMessage.success(`已添加 ${state.uploadedFiles.length} 个附件`)
    }

    // 清空待上传文件
    const clearPendingFiles = () => {
      state.pendingFiles = []
      state.uploadedFiles = []
    }

    // 移除待上传文件
    const removePendingFile = (index: number) => {
      state.pendingFiles.splice(index, 1)
    }

    // 文件数量超限处理
    const fileExceed = () => {
      ElMessage.warning('职位附件不能超过10个')
    }

    // 文件上传前验证
    const beforeFileUpload = (rawFile: any) => {
      if (rawFile.size / 1024 / 1024 > 20) {
        ElMessage.error('文件过大，单个文件不能超过20M')
        return false
      }
      return true
    }

    // 移除已上传文件
    const removeFile = async (id: string) => {
      state.fileList = state.fileList.filter((item: any) => item.id !== id)
    }

    // 清空所有文件
    const clear = () => {
      state.fileList = []
    }

    onMounted(() => {
      nextTick(() => {
        if (fileListRef.value) {
          new Sortable(fileListRef.value, {
            animation: 150,
            handle: '.drag-handle',
            onEnd({ oldIndex, newIndex }) {
              const itemsCopy = [...state.fileList]
              const [removed] = itemsCopy.splice(oldIndex, 1)
              itemsCopy.splice(newIndex, 0, removed)
              // 强制更新数组引用以触发响应式更新
              state.fileList = []
              nextTick(() => {
                state.fileList = itemsCopy
              })
            }
          })
        }
      })
    })

    return {
      ...toRefs(state),
      fileListRef,
      uploadRef,
      overallProgress,
      uploadedCount,
      totalFiles,
      openJobFileUpload,
      handleFileSelect,
      startBatchUpload,
      retryUpload,
      confirmUpload,
      clearPendingFiles,
      removePendingFile,
      removeFile,
      clear,
      fileExceed,
      beforeFileUpload,
      formatFileSize
    }
  }
})
</script>

<style lang="scss" scoped>
.file-list {
  .file {
    display: flex;
    align-items: center;
    margin: 10px 0;
    text-decoration: none;
    color: #409eff;

    .drag-handle {
      cursor: move;
      margin-right: 10px;
      color: #999;
    }

    > div:nth-child(2) {
      flex: 1;
    }

    span {
      cursor: pointer;
    }
  }
}

.upload-demo {
  text-align: center;
  margin-bottom: 20px;

  :deep(.el-upload-dragger) {
    display: flex;
    flex-direction: column;
    justify-content: center;
    div {
      margin: 5px 0;
    }
  }

  .el-button {
    margin-top: 20px;
  }
}

.pending-files {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    font-weight: 500;

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .overall-progress {
    padding: 12px 16px;
    background-color: #fafbfc;
    border-bottom: 1px solid #e4e7ed;

    .progress-text {
      font-size: 12px;
      color: #606266;
    }
  }

  .file-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f2f5;

    &:last-child {
      border-bottom: none;
    }

    .file-info {
      display: flex;
      align-items: center;
      flex: 1;

      .file-icon {
        font-size: 20px;
        color: #909399;
        margin-right: 12px;
      }

      .file-details {
        flex: 1;

        .file-name {
          font-size: 14px;
          color: #303133;
          margin-bottom: 4px;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .file-status {
      display: flex;
      align-items: center;
      margin-right: 16px;
      min-width: 80px;

      .status-waiting {
        display: flex;
        align-items: center;
        color: #909399;
        font-size: 12px;

        .el-icon {
          margin-right: 4px;
        }
      }

      .status-uploading {
        display: flex;
        align-items: center;
        color: #409eff;
        font-size: 12px;

        .el-icon {
          margin-right: 4px;

          &.rotating {
            animation: rotating 2s linear infinite;
          }
        }
      }

      .status-success {
        display: flex;
        align-items: center;
        color: #67c23a;
        font-size: 12px;

        .el-icon {
          margin-right: 4px;
        }
      }

      .status-error {
        display: flex;
        align-items: center;
        color: #f56c6c;
        font-size: 12px;

        .el-icon {
          margin-right: 4px;
        }

        .el-button {
          margin-left: 8px;
        }
      }
    }

    .file-actions {
      .el-button {
        padding: 4px 8px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
